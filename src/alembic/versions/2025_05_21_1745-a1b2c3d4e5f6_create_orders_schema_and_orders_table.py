"""create orders schema and orders table

Revision ID: a1b2c3d4e5f6
Revises: 0cf658516c83
Create Date: 2025-05-21 17:45:00.000000

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects.postgresql import UUID

# revision identifiers, used by Alembic.
revision = "a1b2c3d4e5f6"
down_revision = "0cf658516c83"
branch_labels = None
depends_on = None


def upgrade():
    # Create orders schema
    op.execute("CREATE SCHEMA IF NOT EXISTS orders")

    # Create orders table
    op.create_table(
        "orders",
        sa.Column("id", sa.Integer, primary_key=True, autoincrement=True),
        sa.Column(
            "uuid",
            UUID(as_uuid=True),
            nullable=False,
        ),
        sa.Column("order_date", sa.TIMESTAMP, server_default=sa.func.now()),
        sa.Column("order_by", sa.String(length=255)),
        sa.Column("status", sa.String(length=50)),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("uuid"),
        schema="orders",
    )


def downgrade():
    op.drop_table("orders", schema="orders")
    op.execute("DROP SCHEMA IF EXISTS orders")
