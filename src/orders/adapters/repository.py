from abc import ABC, abstractmethod
from datetime import datetime
from typing import List
from uuid import UUID

from more_itertools import ilen
from sqlalchemy import and_, asc, desc, exists, func, nulls_first, nulls_last, select
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import Session, joinedload

from api.orders.schemas import UpdateOrderStatusRequest, UpdateOrderStatusResponse
from app.config import logger
from common.ordering import Ordering
from common.pagination import Pagination
from common.parser import ParsingError
from common.searching import Searching, apply_search_to_sql
from orders.adapters import orm
from orders.constants import OrderStatus
from orders.domain import model


class AbstractOrdersRepository(ABC):
    @abstractmethod
    def create_order(self, order: model.OrderRequest) -> model.OrderResponse:
        ...

    @abstractmethod
    def update_order_status(
        self, order_id: UUID, update_data: UpdateOrderStatusRequest
    ) -> UpdateOrderStatusResponse:
        ...

    @abstractmethod
    def get_orders(
        self,
        account_id,
        customer_id,
        customer_account_name,
        order_date,
        customer_name,
        customer_email,
        customer_contact_no,
        status_type,
        pagination,
        ordering,
        searching,
    ) -> List[model.OrdersData]:
        ...

    @abstractmethod
    def get_orders_count(
        self,
        account_id,
        customer_id,
        customer_account_name,
        order_date,
        customer_name,
        customer_email,
        customer_contact_no,
        status_type,
        searching,
    ) -> int:
        ...

    @abstractmethod
    def get_order_details(self, order_id: UUID) -> model.OrderDetailsResponse:
        ...


class InMemoryOrdersRepository(AbstractOrdersRepository):
    def create_order(self, order: model.OrderRequest) -> model.OrderResponse:
        ...

    def update_order_status(
        self, order_id: UUID, update_data: UpdateOrderStatusRequest
    ) -> UpdateOrderStatusResponse:
        ...

    def get_orders(
        self,
        account_id,
        customer_id,
        customer_account_name,
        order_date,
        customer_name,
        customer_email,
        customer_contact_no,
        status_type,
        pagination,
        ordering,
        searching,
    ) -> List[model.OrdersData]:
        ...

    def get_orders_count(
        self,
        account_id,
        customer_id,
        customer_account_name,
        order_date,
        customer_name,
        customer_email,
        customer_contact_no,
        status_type,
        searching,
    ) -> int:
        ...

    def get_order_details(self, order_id: UUID) -> model.OrderDetailsResponse:
        ...


class DatabaseOrdersRepository(AbstractOrdersRepository):
    def __init__(self, session: Session) -> None:
        self.session = session
        self.condition_true = True

    def create_order(self, order: model.OrderRequest) -> model.OrderResponse:
        try:
            order_data = model.Order(
                order_by=order.order_by,
                order_date=datetime.now(),
                status=OrderStatus.PENDING.value,
            )
            self.session.add(order_data)
            self.session.flush()

            customer_details = order.customer_details
            customer_details.order_id = order_data.uuid
            self.session.add(customer_details)

            shipping_details = order.shipping_details
            shipping_details.order_id = order_data.uuid
            self.session.add(shipping_details)

            status_history = model.OrderStatusHistory(
                order_id=order_data.uuid,
                status_name=OrderStatus.PENDING.value,
                status_date=datetime.now(),
                comments=order.order_status_history.comments,
            )
            self.session.add(status_history)

            order_item_array = []
            for order_item in order.order_items:
                order_item_array.append(
                    model.OrderItem(
                        order_id=order_data.uuid,
                        sim_type=order_item.sim_type,
                        quantity=order_item.quantity,
                    )
                )

            self.session.add_all(order_item_array)
            self.session.commit()

            return model.OrderResponse(
                order_uuid=order_data.uuid,
                message="Order created successfully",
            )
        except SQLAlchemyError as e:
            logger.error(f"Error creating order: {e}")
            self.session.rollback()
            raise

    def update_order_status(
        self, order_id: UUID, update_data: UpdateOrderStatusRequest
    ) -> UpdateOrderStatusResponse:

        order = (
            self.session.execute(
                select(model.Order).where(orm.orders.columns.uuid == order_id)
            )
            .scalars()
            .first()
        )

        # Check if the order exists
        if not order:
            raise ParsingError(f"Order with ID {order_id} not found")
        else:
            current_status = order.status
            new_status = update_data.status
            order.status = update_data.status

        # Define valid status transitions
        allowed_transitions: dict[str, list[str]] = {
            OrderStatus.PENDING.value: [
                OrderStatus.REJECTED.value,
                OrderStatus.APPROVED.value,
            ],
            OrderStatus.APPROVED.value: [OrderStatus.SHIPPED.value],
            OrderStatus.SHIPPED.value: [],
        }

        # Validate transition
        if current_status in allowed_transitions:
            if new_status not in allowed_transitions[current_status]:
                raise ParsingError(
                    (
                        f"Invalid status transition from {current_status} "
                        f"to {new_status}"
                    )
                )
        else:
            # If current status is not in allowed transitions (e.g., CANCELLED, SHIPPED)
            # disallow changes
            raise ParsingError(
                (
                    f"Cannot update status from {current_status}. "
                    "Status updates not allowed from this state."
                )
            )

        # Check if the order is already completed
        order_status_exist = self.session.query(
            exists().where(
                and_(
                    orm.status_history.columns.order_id == order_id,
                    orm.status_history.columns.status_name == update_data.status,
                )
            )
        ).scalar()
        if order_status_exist:
            raise ParsingError("Cannot update status, Already in the same status")

        # Update tracking if present when the order is shipped
        if update_data.status == OrderStatus.SHIPPED.value:
            if not update_data.tracking:
                raise ParsingError(
                    "Tracking information is required for shipped orders"
                )
            if not update_data.tracking.reference_id:
                raise ParsingError(
                    "Tracking reference ID is required for shipped orders"
                )
            if not update_data.tracking.reference_url:
                raise ParsingError(
                    "Tracking reference URL is required for shipped orders"
                )
        else:
            if update_data.tracking:
                raise ParsingError(
                    "Do not add Tracking information untill not shipped orders"
                )
            if update_data.tracking and update_data.tracking.reference_id:
                raise ParsingError(
                    "Do not add Tracking information ID untill for shipped orders"
                )
            if update_data.tracking and update_data.tracking.reference_url:
                raise ParsingError(
                    "Do not add Tracking information URL untill for shipped orders"
                )

        # Create and add order tracking data only for shipped orders
        if update_data.status == OrderStatus.SHIPPED.value and update_data.tracking:
            order_tracking_data = model.OrderTracking(
                reference_id=update_data.tracking.reference_id,
                reference_url=update_data.tracking.reference_url,
                order_id=order_id,
            )
            self.session.add(order_tracking_data)

        # Update order status
        order_status_data = model.OrderStatusHistory(
            status_name=update_data.status,
            status_date=datetime.now(),
            comments=update_data.comments if update_data.comments else None,
            order_id=order_id,
        )

        self.session.add(order_status_data)
        self.session.commit()

        return UpdateOrderStatusResponse(
            order_id=order_id,
            status=update_data.status,
            message="Order status updated successfully",
        )

    def get_query(
        self,
        account_id,
        customer_id,
        customer_account_name,
        order_date,
        customer_name,
        customer_email,
        customer_contact_no,
        status_type,
        searching: Searching | None = None,
    ):

        query = (
            select(
                model.Order.uuid,
                model.Order.order_by,
                model.Order.order_date,
                model.Order.status,
                model.OrderCustomer.customer_account_name,
                model.OrderCustomer.person_placing_order,
                model.OrderCustomer.customer_account_logo_url,
                model.OrderCustomer.customer_name,
                model.OrderCustomer.customer_email,
                model.OrderCustomer.customer_id,
                model.OrderCustomer.customer_account_id,
                (orm.customers.c.customer_contact_no.label("customer_phone")),
                func.json_agg(
                    func.json_build_object(
                        "type",
                        model.OrderItem.sim_type,
                        "quantity",
                        model.OrderItem.quantity,
                    )
                ).label("sim_details"),
            )
            .join(model.OrderCustomer, model.OrderCustomer.order_id == model.Order.uuid)
            .outerjoin(model.OrderItem, model.OrderItem.order_id == model.Order.uuid)
            .group_by(
                model.Order.uuid,
                model.Order.order_by,
                model.Order.order_date,
                model.OrderCustomer.customer_account_name,
                model.OrderCustomer.customer_id,
                model.OrderCustomer.customer_account_id,
                model.OrderCustomer.customer_account_logo_url,
                model.OrderCustomer.person_placing_order,
                model.OrderCustomer.customer_name,
                model.OrderCustomer.customer_email,
                model.OrderCustomer.customer_contact_no,
                model.Order.status,
            )
        )

        conditions = []

        if account_id is not None:
            conditions.append(model.OrderCustomer.customer_account_id == account_id)

        if customer_id is not None:
            conditions.append(model.OrderCustomer.customer_id == customer_id)

        if customer_account_name is not None:
            conditions.append(
                orm.customers.c.customer_account_name.ilike(
                    f"%{customer_account_name}%"
                )
            )

        if order_date is not None:
            conditions.append(model.Order.order_date >= order_date.start_date)
            conditions.append(model.Order.order_date <= order_date.end_date)

        if customer_name is not None:
            conditions.append(orm.customers.c.customer_name.ilike(f"%{customer_name}%"))

        if customer_email is not None:
            conditions.append(
                orm.customers.c.customer_email.ilike(f"%{customer_email}%")
            )

        if customer_contact_no is not None:
            conditions.append(
                orm.customers.c.customer_contact_no.ilike(f"%{customer_contact_no}%")
            )

        if status_type is not None:
            conditions.append(model.Order.status == status_type)

        if conditions:
            query = query.where(and_(*conditions))
        if searching is not None:
            query = apply_search_to_sql(
                searching=searching, model=model.Order, stmt=query
            )
        return query

    def get_orders(
        self,
        account_id,
        customer_id,
        customer_account_name,
        order_date,
        customer_name,
        customer_email,
        customer_contact_no,
        status_type,
        pagination: Pagination | None = None,
        ordering: Ordering | None = None,
        searching: Searching | None = None,
    ) -> List[model.OrdersData]:
        query = self.get_query(
            account_id=account_id,
            customer_id=customer_id,
            customer_account_name=customer_account_name,
            order_date=order_date,
            customer_name=customer_name,
            customer_email=customer_email,
            customer_contact_no=customer_contact_no,
            status_type=status_type,
            searching=searching,
        )
        if ordering is not None:
            order_direction, null_order = (
                (desc, nulls_last)
                if ordering.order.lower() == "desc"
                else (asc, nulls_first)
            )

            fields_mapper = {
                "order_id": orm.orders.c.uuid,
                "order_date": orm.orders.c.order_date,
                "status": orm.orders.c.status,
                "customer_account_name": orm.customers.c.customer_account_name,
                "person_placing_order": orm.customers.c.person_placing_order,
                "customer_contact_no": orm.customers.c.customer_contact_no,
                "account_id": orm.customers.c.customer_account_id,
                "customer_id": orm.customers.c.customer_id,
                "customer_name": orm.customers.c.customer_name,
                "customer_email": orm.customers.c.customer_email,
            }

            if ordering.field in fields_mapper:
                order_field = fields_mapper[ordering.field]
            else:
                order_field = getattr(model.Order, ordering.field)

            query = query.order_by(null_order(order_direction(order_field))).order_by(
                order_direction(orm.orders.c.uuid)  # Ensure stable ordering by ID
            )
        if pagination:
            query = query.offset(pagination.offset).limit(pagination.page_size)

        rows = []

        for raw in self.session.execute(query).mappings():
            rows.append(
                model.OrdersData(
                    order_id=raw["uuid"],
                    order_by=raw["order_by"],
                    customer_account_name=raw["customer_account_name"],
                    customer_account_logo_url=raw["customer_account_logo_url"],
                    person_placing_order=(
                        raw.get("person_placing_order", "")
                        if raw.get("person_placing_order")
                        else ""
                    ),
                    order_date=raw["order_date"],
                    customer_name=raw["customer_name"],
                    customer_email=raw["customer_email"],
                    customer_phone=raw["customer_phone"],
                    order_status_history=raw["status"],
                    order_item=(
                        raw.get("sim_details", "") if raw.get("sim_details") else []
                    ),
                )
            )
        return rows

    def get_orders_count(
        self,
        account_id,
        customer_id,
        customer_account_name,
        order_date,
        customer_name,
        customer_email,
        customer_contact_no,
        status_type,
        searching,
    ) -> int:
        query = self.get_orders(
            account_id=account_id,
            customer_id=customer_id,
            customer_account_name=customer_account_name,
            order_date=order_date,
            customer_name=customer_name,
            customer_email=customer_email,
            customer_contact_no=customer_contact_no,
            status_type=status_type,
            searching=searching,
        )

        return ilen(query)

    def get_order_details(self, order_id: UUID) -> model.OrderDetailsResponse:
        order = (
            self.session.query(model.Order)
            .options(
                joinedload(getattr(model.Order, "customers")),
                joinedload(getattr(model.Order, "shipping_details")),
                joinedload(getattr(model.Order, "items")),
                joinedload(getattr(model.Order, "status_history")),
                joinedload(getattr(model.Order, "tracking")),
            )
            .filter(model.Order.uuid == order_id)
            .first()
        )

        if not order:
            raise ParsingError(f"Order with ID {order_id} not found")

        # Get the latest status history
        latest_status = (
            max(order.status_history, key=lambda x: x.status_date)
            if order.status_history
            else None
        )

        customer = order.customers[0] if order.customers else None
        shipping = order.shipping_details[0] if order.shipping_details else None
        tracking = order.tracking[0] if order.tracking else None

        # Build the response
        return model.OrderDetailsResponse(
            id=order.id,
            order_id=order.uuid,
            order_by=order.order_by,
            order_date=order.order_date,
            customer_details=customer,
            shipping_details=shipping,
            order_items=order.items,
            order_status_history=latest_status,
            order_tracking=tracking,
        )
