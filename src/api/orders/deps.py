import logging

from fastapi import Depends
from sqlalchemy.orm import Session

from api.deps import get_authenticated_user
from api.deps import get_db_session as _get_db_session
from auth.dto import AuthenticatedUser
from orders.adapters.repository import (
    AbstractOrdersRepository,
    DatabaseOrdersRepository,
)
from orders.proxies import OrdersServiceAuthProxy
from orders.services import AbstractOrdersService, OrdersService


def get_orders_repository(
    session: Session = Depends(_get_db_session),
) -> AbstractOrdersRepository:
    return DatabaseOrdersRepository(session)


def orders_service(
    orders_repository: AbstractOrdersRepository = Depends(get_orders_repository),
    authenticated_user: AuthenticatedUser = Depends(get_authenticated_user),
) -> AbstractOrdersService:
    logging.error("Calling deps service")
    return OrdersServiceAuthProxy(
        orders_service=OrdersService(
            orders_repository,
        ),
        user=authenticated_user,
    )
