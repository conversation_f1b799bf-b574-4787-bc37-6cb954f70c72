# Contributing to Mononglass - Single Pane of Glass API

Thank you for your interest in contributing to Mononglass!

## Local Setup

Follow these steps to set up the project locally:

1. **Create the `.env` file:**
    ```shell
    cd <path-to-project-directory>
    cp example.env src/.env
    vim src/.env
    # fill in the required variables
    ```

2. **Install Poetry:**
    Poetry >= 1.2.0 should be used. To install poetry, run:
    ```shell
    curl -sSL https://install.python-poetry.org | python3 -
    ```
    To upgrade poetry:
    ```shell
    poetry self update
    ```
    Refer to the [official documentation](https://python-poetry.org/docs) for more details.

3. **Configure credentials for private package (Nexus):**
    Get credentials from the Confluence page: [Nexus Artifact Repository](https://nextgenclearing.atlassian.net/wiki/spaces/DEVOPS/pages/446595097/Nexus+Artifact+Repository)
    ```shell
    poetry config http-basic.nexus <username> <password>
    ```

4. **(Optional) Configure Poetry to use the existing environment:**
    See [Managing Environments](https://python-poetry.org/docs/managing-environments/).

5. **Activate the virtual environment:**
    Note: poetry will create one if not configured in the previous step.
    ```shell
    poetry shell
    ```

6. **Install dependencies:**
    ```shell
    make install-all-deps
    ```

7. **Install pre-commit:**
    ```shell
    pre-commit install
    ```

8. **Start the application:**
    ```shell
    cd src
    export APPLICATION_ENV=local
    python -m app.main
    ```

9. **Examine documentation:**
    Visit http://localhost:8000/docs

## Coding Standards

We follow the following coding standards in our project to maintain code consistency:

- [mypy](http://mypy-lang.org/): Type checking for Python
- [flake8](https://flake8.pycqa.org/): Code linting and style checking
- [black](https://black.readthedocs.io/): Code formatting
- [isort](https://pycqa.github.io/isort/): Import sorting


## Running Tests
Please ensure that your change(s) include appropriate tests. To run the test cases, use the following command:

    pytest tests/

Before submitting your merge request, make sure to run the following commands to ensure that your code adheres to our coding standards:

    mypy src/ tests/  # Check types with mypy
    flake8 src/ tests/  # Check linting and style with flake8
    black src/ tests/  # Check code formatting with black
    isort src/ tests/  # Check import sorting with ishort


If you encounter any issues or have feature requests, check our [issue tracker](https://nextgenclearing.atlassian.net/jira/software/projects/SPOG/issues/) before creating a new issue.
